"use client"

import { useState, useEffect, useCallback } from 'react'
import { View, Text, StyleSheet, FlatList, Alert, Modal, ActivityIndicator, ScrollView } from 'react-native'
import { colors } from '../theme/colors'
import { spacing } from '../theme/spacing'
import { textStyles } from '../theme/typography'
import StatusBarManager from '../components/common/StatusBarManager'
import Button from '../components/common/Button'
import CartItem from '../components/cart/CartItem'
import { cartApi, orderApi } from '../api'
import errorHandler from '../utils/errorHandler'
import Input from '../components/common/Input'
import { useFocusEffect } from '@react-navigation/native'

const CartScreen = ({ navigation }) => {
  const [cartItems, setCartItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [editingItem, setEditingItem] = useState(null)
  const [quantity, setQuantity] = useState(1)
  const [deletingItem, setDeletingItem] = useState(null)
  const [isUpdating, setIsUpdating] = useState(false)
  const [placeOrderVisible, setPlaceOrderVisible] = useState(false)
  const [shippingAddress, setShippingAddress] = useState({
    street: "123 Paper Street",
    city: "Mumbai",
    state: "Maharashtra",
    zipCode: "400001",
    country: "India",
  })
  const [notes, setNotes] = useState("")
  const [isPlacingOrder, setIsPlacingOrder] = useState(false)

  // Fetch cart data on component mount
  useEffect(() => {
    fetchCart();
  }, []);

  // Refresh cart data every time the screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log('Cart screen focused, refreshing cart data...');
      fetchCart();
      return () => {
        // Cleanup function when screen is unfocused
        console.log('Cart screen unfocused');
      };
    }, [])
  );

  // State to store cart total from API
  const [cartTotal, setCartTotal] = useState(0)
  const [itemCount, setItemCount] = useState(0)

  // Fetch cart from API
  const fetchCart = async () => {
    setLoading(true)
    try {
      const response = await cartApi.getCart()
      console.log('Cart API response:', response)

      // Handle different response formats
      if (response?.status === "success" && response?.data) {
        // Format from the API response you provided
        setCartItems(response.data.items || [])
        setCartTotal(response.data.total || 0)
        setItemCount(response.data.itemCount || 0)
      } else if (response?.items) {
        // Alternative format
        setCartItems(response.items || [])
        setCartTotal(response.total || 0)
        setItemCount(response.itemCount || response.items.length || 0)
      } else {
        // Fallback if response format is unexpected
        console.warn('Unexpected cart response format:', response)
        setCartItems([])
        setCartTotal(0)
        setItemCount(0)
      }
    } catch (error) {
      console.error('Error fetching cart:', error)
      errorHandler.handleApiError(error, {
        context: "fetch_cart",
        fallbackMessage: "Failed to load cart items. Please try again.",
      })
      // Set empty values on error
      setCartItems([])
      setCartTotal(0)
      setItemCount(0)
    } finally {
      setLoading(false)
    }
  }

  // Format price with commas for better readability
  const formatPrice = (price) => {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
  }

  // Calculate total amount (fallback if API doesn't provide it)
  const calculateTotal = () => {
    // If we have the total from API, use it
    if (cartTotal > 0) {
      return cartTotal
    }

    // Otherwise calculate from items
    return cartItems.reduce((total, item) => {
      return total + (item.stock?.pricePerRoll || 0) * (item.quantity || 0)
    }, 0)
  }

  // Handle edit cart item
  const handleEditItem = (item) => {
    setEditingItem(item)
    setQuantity(item.quantity)
  }

  // Handle update cart item
  const handleUpdateItem = async () => {
    if (!editingItem) return

    setIsUpdating(true)
    try {
      await cartApi.updateCartItem(editingItem.id, quantity)
      setCartItems((prev) => prev.map((item) => (item.id === editingItem.id ? { ...item, quantity } : item)))
      setEditingItem(null)
    } catch (error) {
      errorHandler.handleApiError(error, {
        context: "update_cart",
        fallbackMessage: "Failed to update cart item. Please try again.",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  // Handle delete confirmation
  const handleDeleteConfirmation = (item) => {
    setDeletingItem(item)
  }

  // Handle delete cart item
  const handleDeleteItem = async () => {
    if (!deletingItem) return

    setIsUpdating(true)
    try {
      await cartApi.removeCartItem(deletingItem.id)
      setCartItems((prev) => prev.filter((item) => item.id !== deletingItem.id))
      setDeletingItem(null)
    } catch (error) {
      errorHandler.handleApiError(error, {
        context: "delete_cart",
        fallbackMessage: "Failed to remove cart item. Please try again.",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  // Handle place order
  const handlePlaceOrder = async () => {
    // Validate address fields
    const addressErrors = [];

    if (!shippingAddress.street) {
      addressErrors.push("Street address is required");
    }

    if (!shippingAddress.city) {
      addressErrors.push("City is required");
    }

    if (!shippingAddress.state) {
      addressErrors.push("State is required");
    }

    if (!shippingAddress.zipCode) {
      addressErrors.push("Zip/Postal code is required");
    }

    if (addressErrors.length > 0) {
      Alert.alert("Address Validation Error", addressErrors.join("\n"));
      return;
    }

    // Format shipping address to match API expectations
    const formattedAddress = {
      addressLine1: shippingAddress.street,
      city: shippingAddress.city,
      state: shippingAddress.state,
      postalCode: shippingAddress.zipCode,
      country: shippingAddress.country || "India"
    };

    setIsPlacingOrder(true);
    try {
      console.log("Formatted shipping address:", formattedAddress);
      await orderApi.placeOrder({
        shippingAddress: formattedAddress,
        notes,
      });

      // Clear cart after successful order
      setCartItems([]);
      setPlaceOrderVisible(false);

      // Refresh cart from backend
      fetchCart();

      // Navigate to success screen with reset to prevent back navigation
      navigation.navigate("Success", {
        type: 'order',
        title: 'Order Submitted for Approval',
        message: 'Your order has been submitted for approval. We will notify you once it has been processed.',
        buttonText: 'View Orders',
        buttonDestination: 'OrdersTab'
      });
    } catch (error) {
      console.error("Order placement error:", error);

      // Check for validation errors
      if (error.response?.status === 400 && error.data?.errors) {
        const validationErrors = error.data.errors;
        const errorMessages = validationErrors.map(err => `${err.message}`);
        Alert.alert("Validation Error", errorMessages.join("\n"));
      } else {
        errorHandler.handleApiError(error, {
          context: "place_order",
          fallbackMessage: "Failed to place order. Please try again.",
        });
      }
    } finally {
      setIsPlacingOrder(false);
    }
  }

  // Render cart item
  const renderCartItem = ({ item }) => (
    <CartItem item={item} onEdit={handleEditItem} onDelete={handleDeleteConfirmation} />
  )

  // Render edit item modal
  const renderEditModal = () => {
    if (!editingItem) return null

    return (
      <Modal visible={!!editingItem} transparent animationType="slide" onRequestClose={() => setEditingItem(null)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Edit Cart Item</Text>

            <View style={styles.itemDetails}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Type:</Text>
                <Text style={styles.detailValue}>{editingItem.stock.type}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>GSM:</Text>
                <Text style={styles.detailValue}>{editingItem.stock.gsm}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>BF:</Text>
                <Text style={styles.detailValue}>{editingItem.stock.bf}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Price:</Text>
                <Text style={styles.detailValue}>₹{editingItem.stock.pricePerRoll} per Kg</Text>
              </View>
            </View>

            <View style={styles.quantityContainer}>
              <Text style={styles.quantityLabel}>Quantity:</Text>
              <View style={styles.quantityInputContainer}>
                <Button
                  title="-"
                  onPress={() => setQuantity((prev) => Math.max(1, prev - 1))}
                  disabled={quantity <= 1}
                  style={styles.quantityButton}
                  textStyle={styles.quantityButtonText}
                />
                <Text style={styles.quantityText}>{quantity}</Text>
                <Button
                  title="+"
                  onPress={() => setQuantity((prev) => prev + 1)}
                  style={styles.quantityButton}
                  textStyle={styles.quantityButtonText}
                />
              </View>
            </View>

            <View style={styles.modalActions}>
              <Button
                title="Cancel"
                variant="outline"
                onPress={() => setEditingItem(null)}
                style={styles.modalButton}
              />
              <Button title="Update" onPress={handleUpdateItem} loading={isUpdating} style={styles.modalButton} />
            </View>
          </View>
        </View>
      </Modal>
    )
  }

  // Render delete confirmation modal
  const renderDeleteModal = () => {
    if (!deletingItem) return null

    return (
      <Modal visible={!!deletingItem} transparent animationType="slide" onRequestClose={() => setDeletingItem(null)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Confirm Removal</Text>
            <Text style={styles.modalMessage}>Are you sure you want to remove this item from the cart?</Text>

            <View style={styles.modalActions}>
              <Button title="No" variant="outline" onPress={() => setDeletingItem(null)} style={styles.modalButton} />
              <Button title="Yes" onPress={handleDeleteItem} loading={isUpdating} style={styles.modalButton} />
            </View>
          </View>
        </View>
      </Modal>
    )
  }

  // Render place order modal
  const renderPlaceOrderModal = () => {
    return (
      <Modal
        visible={placeOrderVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setPlaceOrderVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.orderModalContent}>
            <Text style={styles.modalTitle}>Send Order for Approval</Text>
            
            <ScrollView showsVerticalScrollIndicator={false} style={styles.orderScrollView}>
              <Text style={styles.sectionTitle}>Shipping Address</Text>
              
              <Input
                label="Street Address"
                value={shippingAddress.street}
                onChangeText={(text) => setShippingAddress((prev) => ({ ...prev, street: text }))}
                placeholder="Enter street address"
              />

              <Input
                label="City"
                value={shippingAddress.city}
                onChangeText={(text) => setShippingAddress((prev) => ({ ...prev, city: text }))}
                placeholder="Enter city"
              />

              <View style={styles.addressRow}>
                <Input
                  label="State"
                  value={shippingAddress.state}
                  onChangeText={(text) => setShippingAddress((prev) => ({ ...prev, state: text }))}
                  placeholder="Enter state"
                  style={styles.addressRowInput}
                />

                <Input
                  label="Zip Code"
                  value={shippingAddress.zipCode}
                  onChangeText={(text) => setShippingAddress((prev) => ({ ...prev, zipCode: text }))}
                  placeholder="Enter zip code"
                  keyboardType="number-pad"
                  style={styles.addressRowInput}
                />
              </View>

              <Input
                label="Country"
                value={shippingAddress.country}
                onChangeText={(text) => setShippingAddress((prev) => ({ ...prev, country: text }))}
                placeholder="Enter country"
                style={{ marginBottom: spacing.medium }}
              />

              <Input
                label="Order Notes (Optional)"
                value={notes}
                onChangeText={setNotes}
                placeholder="Enter any special instructions"
                multiline
                numberOfLines={3}
                style={styles.notesInput}
              />

              <View style={styles.orderSummary}>
                <Text style={styles.summaryTitle}>Order Summary</Text>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Total Items:</Text>
                  <Text style={styles.summaryValue}>{itemCount || cartItems.length}</Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Total Amount:</Text>
                  <Text style={styles.summaryValue}>₹{formatPrice(calculateTotal())}</Text>
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalActions}>
              <Button
                title="Cancel"
                variant="outline"
                onPress={() => setPlaceOrderVisible(false)}
                style={styles.modalButton}
              />
              <Button
                title="Submit"
                onPress={handlePlaceOrder}
                loading={isPlacingOrder}
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    )
  }

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Cart</Text>
      </View>

      <View style={styles.content}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Loading cart...</Text>
          </View>
        ) : cartItems.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Your cart is empty</Text>
            <Button
              title="Browse Products"
              variant="outline"
              onPress={() => navigation.navigate("HomeTab")}
              style={styles.emptyButton}
            />
          </View>
        ) : (
          <>
            <FlatList
              data={cartItems}
              renderItem={renderCartItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContainer}
              showsVerticalScrollIndicator={false}
            />

            <View style={styles.footer}>
              <View style={styles.totalContainer}>
                <View>
                  <Text style={styles.totalLabel}>Total:</Text>
                  <Text style={styles.itemCount}>{itemCount || cartItems.length} items</Text>
                </View>
                <Text style={styles.totalAmount}>₹{formatPrice(calculateTotal())}</Text>
              </View>

              <Button title="Send Order for Approval" onPress={() => setPlaceOrderVisible(true)} style={styles.checkoutButton} />
            </View>
          </>
        )}
      </View>

      {renderEditModal()}
      {renderDeleteModal()}
      {renderPlaceOrderModal()}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: "center",
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: "bold",
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: spacing.medium,
  },
  listContainer: {
    paddingBottom: 100, // Make space for footer
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginTop: spacing.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginBottom: spacing.medium,
  },
  emptyButton: {
    width: 200,
  },
  footer: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.white,
    padding: spacing.medium,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    elevation: 5,
  },
  totalContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.medium,
  },
  totalLabel: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "500",
  },
  itemCount: {
    ...textStyles.caption,
    color: colors.textLight,
    marginTop: 2,
  },
  totalAmount: {
    ...textStyles.heading3,
    color: colors.primary,
    fontWeight: "bold",
  },
  checkoutButton: {
    width: "100%",
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: spacing.medium,
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    width: "90%",
    padding: spacing.medium,
    maxHeight: "80%",
  },
  orderModalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    width: "90%",
    padding: spacing.medium,
    maxHeight: "85%",
    elevation: 5,
  },
  orderScrollView: {
    maxHeight: 400,
  },
  sectionTitle: {
    ...textStyles.heading4 || { ...textStyles.body1, fontWeight: 'bold' },
    marginBottom: spacing.medium,
    color: colors.primary,
  },
  modalTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.medium,
    textAlign: "center",
  },
  modalMessage: {
    ...textStyles.body1,
    color: colors.textDark,
    marginBottom: spacing.large,
    textAlign: "center",
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.medium,
    borderTopWidth: 1,
    borderTopColor: colors.border || '#eee',
    paddingTop: spacing.medium,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: spacing.tiny,
    
  },
  itemDetails: {
    marginBottom: spacing.medium,
  },
  detailRow: {
    flexDirection: "row",
    marginBottom: spacing.small,
  },
  detailLabel: {
    ...textStyles.body2,
    color: colors.textLight,
    width: 80,
  },
  detailValue: {
    ...textStyles.body1,
    color: colors.textDark,
  },
  quantityContainer: {
    marginBottom: spacing.large,
  },
  quantityLabel: {
    ...textStyles.body1,
    color: colors.textDark,
    marginBottom: spacing.small,
    fontWeight: "bold",
  },
  quantityInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  quantityButton: {
    // width: 40,
    // height: 40,
    borderRadius: 10,
    padding: 0,
  },
  quantityButtonText: {
    fontSize: 24,
  },
  quantityText: {
    ...textStyles.body1,
    color: colors.textDark,
    marginHorizontal: spacing.medium,
    width: 30,
    textAlign: "center",
  },
  addressRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  addressRowInput: {
    flex: 1,
    marginHorizontal: spacing.tiny,
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
    marginBottom: spacing.medium,
  },
  orderSummary: {
    marginVertical: spacing.medium,
    padding: spacing.medium,
    backgroundColor: colors.card || colors.background,
    borderRadius: 8,
    elevation: 1,
  },
  summaryTitle: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "600",
    marginBottom: spacing.small,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.small,
  },
  summaryLabel: {
    ...textStyles.body2,
    color: colors.textLight,
  },
  summaryValue: {
    ...textStyles.body2,
    color: colors.textDark,
    fontWeight: "500",
  },
})

export default CartScreen
